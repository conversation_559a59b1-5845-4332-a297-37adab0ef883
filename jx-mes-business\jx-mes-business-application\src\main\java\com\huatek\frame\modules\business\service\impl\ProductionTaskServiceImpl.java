package com.huatek.frame.modules.business.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;

import javax.validation.Validator;
import java.lang.reflect.Field;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.huatek.frame.common.annotation.poi.ExcelExportConversion;
import com.huatek.frame.common.annotation.poi.ExcelImportConversion;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huatek.frame.common.context.SecurityContextHolder;
import com.huatek.frame.modules.business.constant.BusinessConstant;
import com.huatek.frame.modules.business.domain.*;
import com.huatek.frame.modules.business.domain.vo.*;
import com.huatek.frame.modules.business.mapper.CapabilityAssetMapper;
import com.huatek.frame.modules.business.mapper.ProdTaskEqInfoMapper;
import com.huatek.frame.modules.business.mapper.ProdTaskOpHistMapper;
import com.huatek.frame.modules.business.mapper.ProductionTaskAttachmentsMapper;
import com.huatek.frame.modules.business.mapper.ProductionTaskMapper;
import com.huatek.frame.modules.business.mapper.ProductionTaskTestDataMapper;
import com.huatek.frame.modules.business.service.AbnormalfeedbackService;
import com.huatek.frame.modules.business.service.CodeManagementService;
import com.huatek.frame.modules.business.service.OutsourcingService;
import com.huatek.frame.modules.business.service.ProductionTaskService;
import com.huatek.frame.modules.business.service.WorkstationService;
import com.huatek.frame.modules.business.service.AwaitingProductionOrderService;
import com.huatek.frame.modules.business.service.ProdTaskOpHistService;
import com.huatek.frame.modules.business.service.dto.*;
import com.huatek.frame.modules.constant.DicConstant;
import com.huatek.frame.modules.system.domain.vo.CascadeOptionsVO;
import com.huatek.frame.common.utils.StringUtils;
import com.huatek.frame.common.exception.ServiceException;
import com.huatek.frame.common.utils.bean.BeanValidators;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import com.huatek.frame.modules.system.domain.vo.SysGroupVO;
import com.huatek.frame.modules.system.service.SysGroupService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.huatek.frame.common.annotation.datascope.DataScope;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.common.utils.SecurityUser;
import com.huatek.frame.common.utils.HuatekTools;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * 生产任务 ServiceImpl
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Service
@DubboService
//@CacheConfig(cacheNames = "productionTask")
//@RefreshScope
@Slf4j
public class ProductionTaskServiceImpl implements ProductionTaskService {

    public static final String MATCH_MULTIPLE_VALUE_REGEXP = "(^|,)(#)(,|$)";

    @Autowired
    private SecurityUser securityUser;

    @Autowired
    private ProductionTaskMapper productionTaskMapper;

    @Autowired
    private CapabilityAssetMapper capabilityAssetMapper;

    @Autowired
    private ProductionTaskTestDataMapper productionTaskTestDataMapper;

    @Autowired
    private ProdTaskEqInfoMapper prodTaskEqInfoMapper;

    @Autowired
    private ProductionTaskAttachmentsMapper productionTaskAttachmentsMapper;

    @Autowired
    private ProdTaskOpHistMapper prodTaskOpHistMapper;

    @Autowired
    protected Validator validator;

    private Map<String, Function<String, Page<SelectOptionsVO>>> selectOptionsFuncMap = new HashMap<>();

    @Autowired
    private AbnormalfeedbackService abnormalfeedbackService;

    @Autowired
    private OutsourcingService outsourcingService;

    @Autowired
    private CodeManagementService codeManagementService;

    @Autowired
    private SysGroupService sysGroupService;

    @Autowired
    private WorkstationService workstationService;

    @Autowired
    private AwaitingProductionOrderService awaitingProductionOrderService;

    @Autowired
    private ProdTaskOpHistService prodTaskOpHistService;

    public ProductionTaskServiceImpl() {

    }

    @Override
    //@Cacheable(keyGenerator = "keyGenerator")
    @DataScope(groupAlias = "t", userAlias = "t")
    public TorchResponse<List<ProductionTaskVO>> findProductionTaskPage(ProductionTaskDTO dto) {

        //查询当前用户角色
        List<String> roles = productionTaskMapper.selectCurrentUserRoles(SecurityContextHolder.getCurrentUserId());

        //用户部门和班组
          TorchResponse<SysGroupVO> group = sysGroupService.findGroup(SecurityContextHolder.getCurrentUserGroupId());
          String groupCode = group.getData().getData().getGroupCode();

          boolean isBanzu=false;

        //非管理员 和非调度
        if (!dto.getIsAdmin() && !roles.contains(DicConstant.Role.ROLE_DIAODU)) {
            if (BusinessConstant.PRODUCTIONTASK_TODO.equals(dto.getToDoOrAll())) {
                if(roles.contains(BusinessConstant.ROLE_RENWUSHENPIREN)){
                    if(groupCode.equals(DicConstant.Group.GROUP_KEKAOXING)){
                        dto.setResponsiblePerson(SecurityContextHolder.getCurrentUserGroupId());
                    }else{
                        isBanzu=true;
                    }
                    dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE );
                }
                else{
                    isBanzu=true;
                    if (roles.contains(BusinessConstant.ROLE_SHENGCHANBUBANZUZHANG) || roles.contains(BusinessConstant.ROLE_KEKAOXINGBANZUZHANG)) {
                        if (StringUtils.isEmpty(dto.getStatus())) {
                            dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI + "," +
                                    DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG + "," +
                                    DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING + "," +
                                    DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI);
                        } else {
                            //如果传的status不在这几个钟就返回空
                            if (!dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI) &&
                                    !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG) &&
                                    !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING) &&
                                    !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI)) {
                                dto.setStatus("");
                            }
                        }
                    } else {
                        if (StringUtils.isEmpty(dto.getStatus())) {
                            dto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG + "," +
                                    DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING + "," +
                                    DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI);
                        } else {
                            //如果传的status不在这几个钟就返回空
                            if (!dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG) &&
                                    !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING) &&
                                    !dto.getStatus().contains(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI)) {
                                dto.setStatus("");
                            }
                        }
                    }
                }


            } else {
                //全部 只有班组长有权限看
                if (!roles.contains(BusinessConstant.ROLE_SHENGCHANBUBANZUZHANG) &&
                        !roles.contains(BusinessConstant.ROLE_KEKAOXINGBANZUZHANG)) {
                    dto.setStatus("");
                }
            }

            if(isBanzu){
                dto.setDepartment(SecurityContextHolder.getCurrentUserGroupId());
            }


        }

        PageHelper.startPage(dto.getPage(), dto.getLimit());
        Page<ProductionTaskVO> productionTasks = productionTaskMapper.selectProductionTaskPage(dto);
        TorchResponse<List<ProductionTaskVO>> response = new TorchResponse<List<ProductionTaskVO>>();
        response.getData().setData(productionTasks);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(productionTasks.getTotal());
        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse saveOrUpdate(ProductionTaskDTO productionTaskDto) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        if (HuatekTools.isEmpty(productionTaskDto.getCodexTorchDeleted())) {
            productionTaskDto.setCodexTorchDeleted(Constant.DEFAULT_NO);
        }
        String id = productionTaskDto.getId();
        ProductionTask entity = new ProductionTask();
        BeanUtils.copyProperties(productionTaskDto, entity);
        entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
        entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
        entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));


        // 保存或更新主表
        if (HuatekTools.isEmpty(id)) {
            TorchResponse response = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_SCRW);
            entity.setTaskNumber(response.getData().getData().toString());
            entity.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI);
            productionTaskMapper.insert(entity);
        } else {
            productionTaskMapper.updateById(entity);
            // 更新时先删除子表数据
            deleteChildTableData(entity.getTaskNumber());
            // 保存子表数据
            saveChildTableData(productionTaskDto, entity.getTaskNumber());
        }


        TorchResponse response = new TorchResponse();
        ProductionTaskVO vo = new ProductionTaskVO();
        BeanUtils.copyProperties(entity, vo);
        response.getData().setData(vo);
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    //@Cacheable(key = "#p0")
    public TorchResponse<ProductionTaskVO> findProductionTask(String id) {
        ProductionTaskVO vo = new ProductionTaskVO();
        if (!HuatekTools.isEmpty(id)) {
            ProductionTask entity = productionTaskMapper.selectById(id);
            if (HuatekTools.isEmpty(entity)) {
                throw new ServiceException("查询失败");
            }
            BeanUtils.copyProperties(entity, vo);
        }
        TorchResponse<ProductionTaskVO> response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(vo);
        return response;
    }

    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse delete(String[] ids) {
        List<ProductionTask> productionTaskList = productionTaskMapper.selectBatchIds(Arrays.asList(ids));
        for (ProductionTask productionTask : productionTaskList) {
            productionTask.setCodexTorchDeleted(Constant.DEFAULT_YES);
            productionTaskMapper.updateById(productionTask);
        }
        //productionTaskMapper.deleteBatchIds(Arrays.asList(ids));
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    public TorchResponse getOptionsList(String id) {
        if (selectOptionsFuncMap.size() == 0) {
            //初始化外键函数
            selectOptionsFuncMap.put("technicalCompetencyNumber", productionTaskMapper::selectOptionsByTechnicalCompetencyNumber);
        }

        //默认分页(分页大小暂时固定为1000，改为分页查询后在动态处理)
        PageHelper.startPage(1, 1000);
        Page<SelectOptionsVO> selectOptionsVOs = new Page<>();
        Function<String, Page<SelectOptionsVO>> pageFunction = selectOptionsFuncMap.get(id);
        if (!HuatekTools.isEmpty(pageFunction)) {
            selectOptionsVOs = pageFunction.apply(id);
        }

        TorchResponse<List<SelectOptionsVO>> response = new TorchResponse<List<SelectOptionsVO>>();
        response.getData().setData(selectOptionsVOs);
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setCount(selectOptionsVOs.getTotal());
        return response;
    }


    @Override
    public TorchResponse getLinkageData(String linkageDataTableName, String conditionalValue) {
        Map<String, String> data = new HashMap();
        try {
            switch (linkageDataTableName) {
                case "capability_asset":
                    data = selectDataLinkageByTechnicalCompetencyNumber(conditionalValue);
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new ServiceException("查询数据异常，请联系管理员！");
        }
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(data);
        return response;
    }

    @Override
    public Map<String, String> selectDataLinkageByTechnicalCompetencyNumber(String capability_number) {
        return productionTaskMapper.selectDataLinkageByTechnicalCompetencyNumber(capability_number);
    }

    @Override
    @ExcelExportConversion(tableName = "production_task", convertorFields = "status,ticketLevel,testMethodology,testType,failureMode")
    @DataScope(groupAlias = "t", userAlias = "t")
    public List<ProductionTaskVO> selectProductionTaskList(ProductionTaskDTO dto) {
        return productionTaskMapper.selectProductionTaskList(dto);
    }

    /**
     * 导入生产任务数据
     *
     * @param productionTaskList 生产任务数据列表
     * @param unionColumns       作为确认数据唯一性的字段集合
     * @param isUpdateSupport    是否更新支持，如果已存在，则进行更新数据
     * @param operName           操作用户
     * @return 结果
     */
    @Override
    @ExcelImportConversion(tableName = "production_task", convertorFields = "status,ticketLevel,testMethodology,testType,failureMode")
    public TorchResponse importProductionTask(List<ProductionTaskVO> productionTaskList, List<String> unionColumns, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(productionTaskList) || productionTaskList.size() == 0) {
            throw new ServiceException("导入生产任务数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (ProductionTaskVO vo : productionTaskList) {
            try {
                ProductionTask productionTask = new ProductionTask();
                if (linkedDataValidityVerification(vo, failureNum, failureMsg)) {
                    failureNum++;
                    continue;
                }
                BeanUtils.copyProperties(vo, productionTask);
                QueryWrapper<ProductionTask> wrapper = new QueryWrapper();
                ProductionTask oldProductionTask = null;
                // 验证是否存在这条数据
                if (!HuatekTools.isEmpty(unionColumns) && unionColumns.size() > 0) {
                    for (String unionColumn : unionColumns) {
                        try {
                            Field field = ProductionTaskVO.class.getDeclaredField(unionColumn);
                            field.setAccessible(true);
                            Object value = field.get(vo);
                            String dbColumnName = StrUtil.toUnderlineCase(unionColumn);
                            wrapper.eq(dbColumnName, value);
                        } catch (Exception e) {
                            throw new ServiceException("导入数据失败");
                        }
                    }
                    List<ProductionTask> oldProductionTaskList = productionTaskMapper.selectList(wrapper);
                    if (!CollectionUtils.isEmpty(oldProductionTaskList) && oldProductionTaskList.size() > 1) {
                        productionTaskMapper.delete(wrapper);
                    } else if (!CollectionUtils.isEmpty(oldProductionTaskList) && oldProductionTaskList.size() == 1) {
                        oldProductionTask = oldProductionTaskList.get(0);
                    }
                }
                if (StringUtils.isNull(oldProductionTask)) {
                    BeanValidators.validateWithException(validator, vo);
                    productionTaskMapper.insert(productionTask);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、任务编号 " + vo.getTaskNumber() + " 导入成功");
                } else if (isUpdateSupport) {
                    BeanValidators.validateWithException(validator, vo);
                    BeanUtil.copyProperties(vo, oldProductionTask, CopyOptions.create().setIgnoreNullValue(true).setIgnoreError(true));
                    productionTaskMapper.updateById(oldProductionTask);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、任务编号 " + vo.getTaskNumber() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、任务编号 " + vo.getTaskNumber() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、任务编号 " + vo.getTaskNumber() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "导入失败" + failureNum + " 条数据，错误如下：");
        }

        Map<String, Object> map = new HashMap<>();
        map.put("success", "导入成功 " + successNum + "条数据");
        map.put("failure", failureMsg);
        TorchResponse response = new TorchResponse<>();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.getData().setData(map);
        return response;
    }

    private Boolean linkedDataValidityVerification(ProductionTaskVO vo, int failureNum, StringBuilder failureMsg) {
        int failureRecord = 0;
        StringBuilder failureRecordMsg = new StringBuilder();
        StringBuilder failureNotNullMsg = new StringBuilder();
        if (!HuatekTools.isEmpty(vo.getTechnicalCompetencyNumber())) {
            List<String> technicalCompetencyNumberList = Arrays.asList(vo.getTechnicalCompetencyNumber().split(","));
            List<CapabilityAsset> list = capabilityAssetMapper.selectList(new QueryWrapper<CapabilityAsset>().in("capability_number", technicalCompetencyNumberList));
            if (CollectionUtils.isEmpty(list)) {
                failureRecord++;
                failureRecordMsg.append("技术能力编号=" + vo.getTechnicalCompetencyNumber() + "; ");
            }
        }
        if (failureRecord > 0) {
            failureNum++;
            failureMsg.append("<br/>" + failureNum + "、");
            if (failureNotNullMsg.length() > 0) {
                failureMsg.append("数据空值校验未通过:" + failureNotNullMsg);
            }
            if (failureRecordMsg.length() > 0) {
                failureMsg.append("关联数据异常:" + failureRecordMsg + "不存在!");
            }
            return true;
        }
        return false;
    }

    /**
     * 删除子表数据
     *
     * @param taskNumber 任务编号
     */
    private void deleteChildTableData(String taskNumber) {
        if (!HuatekTools.isEmpty(taskNumber)) {
            // 删除试验数据
            QueryWrapper<ProductionTaskTestData> testDataWrapper = new QueryWrapper<>();
            testDataWrapper.eq("task_number", taskNumber);
            productionTaskTestDataMapper.delete(testDataWrapper);

            // 删除设备信息
            QueryWrapper<ProdTaskEqInfo> eqInfoWrapper = new QueryWrapper<>();
            eqInfoWrapper.eq("task_number", taskNumber);
            prodTaskEqInfoMapper.delete(eqInfoWrapper);

            // 删除附件信息
            QueryWrapper<ProductionTaskAttachments> attachmentWrapper = new QueryWrapper<>();
            attachmentWrapper.eq("task_number", taskNumber);
            productionTaskAttachmentsMapper.delete(attachmentWrapper);
        }
    }

    /**
     * 保存子表数据
     *
     * @param productionTaskDto 生产任务DTO
     * @param taskNumber        任务编号
     */
    private void saveChildTableData(ProductionTaskDTO productionTaskDto, String taskNumber) {
        String currentUserId = SecurityContextHolder.getCurrentUserId();
        String currentUserGroupId = SecurityContextHolder.getCurrentUserGroupId();
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        // 保存试验数据
        if (!CollectionUtils.isEmpty(productionTaskDto.getProductionTaskTestDataList())) {
            for (ProductionTaskTestDataDTO testDataDto : productionTaskDto.getProductionTaskTestDataList()) {
                ProductionTaskTestData testData = new ProductionTaskTestData();
                BeanUtils.copyProperties(testDataDto, testData);
                testData.setTaskNumber(taskNumber);
                testData.setCodexTorchCreatorId(currentUserId);
                testData.setCodexTorchGroupId(currentUserGroupId);
                testData.setCodexTorchCreateDatetime(currentTime);
                testData.setCodexTorchUpdateDatetime(currentTime);
                testData.setCodexTorchDeleted(Constant.DEFAULT_NO);
                productionTaskTestDataMapper.insert(testData);
            }
        }

        // 保存设备信息
        if (!CollectionUtils.isEmpty(productionTaskDto.getProdTaskEqInfoList())) {
            for (ProdTaskEqInfoDTO eqInfoDto : productionTaskDto.getProdTaskEqInfoList()) {
                ProdTaskEqInfo eqInfo = new ProdTaskEqInfo();
                BeanUtils.copyProperties(eqInfoDto, eqInfo);
                eqInfo.setTaskNumber(taskNumber);
                eqInfo.setCodexTorchCreatorId(currentUserId);
                eqInfo.setCodexTorchGroupId(currentUserGroupId);
                eqInfo.setCodexTorchCreateDatetime(currentTime);
                eqInfo.setCodexTorchUpdateDatetime(currentTime);
                eqInfo.setCodexTorchDeleted(Constant.DEFAULT_NO);
                prodTaskEqInfoMapper.insert(eqInfo);
            }
        }

        // 保存附件信息
        if (!CollectionUtils.isEmpty(productionTaskDto.getProdTaskAttachmentList())) {
            for (ProductionTaskAttachmentsDTO attachmentDto : productionTaskDto.getProdTaskAttachmentList()) {
                ProductionTaskAttachments attachment = new ProductionTaskAttachments();
                BeanUtils.copyProperties(attachmentDto, attachment);
                attachment.setTaskNumber(taskNumber);
                attachment.setCodexTorchCreatorId(currentUserId);
                attachment.setCodexTorchGroupId(currentUserGroupId);
                attachment.setCodexTorchCreateDatetime(currentTime);
                attachment.setCodexTorchUpdateDatetime(currentTime);
                attachment.setCodexTorchDeleted(Constant.DEFAULT_NO);
                productionTaskAttachmentsMapper.insert(attachment);
            }
        }
    }

    private Map<String, String> getAllCascadeOptions(List<CascadeOptionsVO> list, Map<String, String> cascadeOptionsMap) {
        for (CascadeOptionsVO cascadeOptionsVO : list) {
            cascadeOptionsMap.put(cascadeOptionsVO.getValue(), cascadeOptionsVO.getLabel());
            List<CascadeOptionsVO> children = cascadeOptionsVO.getChildren();
            if (!CollectionUtils.isEmpty(children)) {
                getAllCascadeOptions(children, cascadeOptionsMap);
            }
        }
        return cascadeOptionsMap;
    }

    @Override
    public TorchResponse selectProductionTaskListByIds(List<String> ids) {
        List<ProductionTaskVO> productionTaskList = productionTaskMapper.selectProductionTaskListByIds(ids);

        TorchResponse<List<ProductionTaskVO>> response = new TorchResponse<List<ProductionTaskVO>>();
        response.getData().setData(productionTaskList);
        response.setStatus(200);
        response.getData().setCount((long) productionTaskList.size());
        return response;
    }


    @SuppressWarnings("rawtypes")
    @Override
    //@CacheEvict(allEntries = true)
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse abnormalfeedback(AbnormalfeedbackDTO abnormalfeedbackDto) {

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);


        abnormalfeedbackDto.setSourceModule("生产管理-生产任务管理");
        ProductionTask entity = productionTaskMapper.selectById(abnormalfeedbackDto.getSourceId());

        TorchResponse<AbnormalfeedbackVO> torchResponse = abnormalfeedbackService.saveOrUpdate(abnormalfeedbackDto);
        AbnormalfeedbackVO vo = torchResponse.getData().getData();


        if (HuatekTools.isEmpty(entity.getAssocExceptionFeedbackNum())) {
            entity.setAssocExceptionFeedbackNum(vo.getAbnormalNumber());
        } else {
            entity.setAssocExceptionFeedbackNum(entity.getAssocExceptionFeedbackNum() + "," + vo.getAbnormalNumber());
        }

        productionTaskMapper.updateById(entity);

        return response;
    }

    @Override
    public TorchResponse outsourcing(AddOrUpdateOutsourcingDTO addOrUpdateOutsourcingDTO) {
        addOrUpdateOutsourcingDTO.setEntireOrProcess(DicConstant.ProductionOrder.OUTSOURCING_TYPE_PROCESS);


        //addOrUpdateOutsourcingDTO.getProcessId()查询production_task更新状态为暂停中,暂停原因为已申请外协
        ProductionTask productionTask = productionTaskMapper.selectById(addOrUpdateOutsourcingDTO.getProcessId());
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING);
        productionTask.setPauseReason(DicConstant.ProductionOrder.PRODUCTION_PAUSE_REASON_WAIXIE);
        productionTaskMapper.updateById(productionTask);

        return outsourcingService.saveOrUpdate(addOrUpdateOutsourcingDTO);
    }

    @Override
    public TorchResponse saveBatch(List<ProductionTaskDTO> productionTaskDtoList) {

        for (ProductionTaskDTO productionTaskDto : productionTaskDtoList) {
            ProductionTask entity = new ProductionTask();
            BeanUtils.copyProperties(productionTaskDto, entity);
            entity.setCodexTorchCreatorId(SecurityContextHolder.getCurrentUserId());
            entity.setCodexTorchGroupId(SecurityContextHolder.getCurrentUserGroupId());
            entity.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));

            TorchResponse response = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_SCRW);
            entity.setTaskNumber(response.getData().getData().toString());
            entity.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI);
            productionTaskMapper.insert(entity);

        }

        TorchResponse response = new TorchResponse();

        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse splitOrder(List<SplitOrderProductionTaskDTO> splitOrderProductionTaskDTOList) {
        String currentUser = SecurityContextHolder.getCurrentUserName();
        String currentUserId = SecurityContextHolder.getCurrentUserId();
        String currentUserGroupId = SecurityContextHolder.getCurrentUserGroupId();
        Timestamp currentTime = new Timestamp(System.currentTimeMillis());

        ProductionTask originalTask = null;
        Integer totalSplitQuantity = 0;

        // 先计算总的分单数量，验证合格数量是否足够
        for (SplitOrderProductionTaskDTO splitDto : splitOrderProductionTaskDTOList) {
            if (!DicConstant.CommonDic.DEFAULT_ONE.equals(splitDto.getIsSplit())) {
                totalSplitQuantity += splitDto.getInspectionQuantity();
            }
        }

        for (SplitOrderProductionTaskDTO splitDto : splitOrderProductionTaskDTOList) {
            if (DicConstant.CommonDic.DEFAULT_ONE.equals(splitDto.getIsSplit())) {
                // 如果是被分单的记录，更新原记录
                originalTask = productionTaskMapper.selectOne(
                        new QueryWrapper<ProductionTask>()
                                .eq("work_order_number", splitDto.getWorkOrderNumber())
                                .eq("codex_torch_deleted", Constant.DEFAULT_NO)
                );

                if (originalTask != null) {
                    // 获取原任务的合格数量
                    Integer originalQualified = originalTask.getQualifiedQuantity() != null ? originalTask.getQualifiedQuantity() : 0;
                    Integer originalUnqualified = originalTask.getUnqualifiedQuantity() != null ? originalTask.getUnqualifiedQuantity() : 0;

                    // 验证合格数量是否足够分单
                    if (originalQualified < totalSplitQuantity) {
                        throw new ServiceException("合格数量不足，无法完成分单操作。当前合格数量：" + originalQualified + "，需要分单数量：" + totalSplitQuantity);
                    }

                    // 更新原任务的送检数量
                    originalTask.setInspectionQuantity2(splitDto.getInspectionQuantity());

                    // 更新原任务的合格数量（减去分出的数量）
                    originalTask.setQualifiedQuantity(originalQualified - totalSplitQuantity);

                    // 不合格数量保持不变
                    originalTask.setUnqualifiedQuantity(originalUnqualified);

                    // 重新计算完成数量
                    originalTask.setCompletedQuantity(originalTask.getQualifiedQuantity() + originalTask.getUnqualifiedQuantity());

                    originalTask.setCodexTorchUpdateDatetime(currentTime);
                    productionTaskMapper.updateById(originalTask);
                }
            } else {
                // 如果否，复制一份被分单的记录
                if (originalTask != null) {
                    ProductionTask newTask = new ProductionTask();
                    BeanUtils.copyProperties(originalTask, newTask);

                    // 重置主键和关键字段
                    newTask.setId(null);

                    // 生成新的任务编号
                    TorchResponse response = codeManagementService.getOrderNumber(BusinessConstant.CAPABILITY_SCRW);
                    newTask.setTaskNumber(response.getData().getData().toString());

                    // 设置新任务的送检数量
                    newTask.setInspectionQuantity2(splitDto.getInspectionQuantity());

                    // 新任务的合格数量等于分单数量（从原任务的合格数量中分出）
                    newTask.setQualifiedQuantity(splitDto.getInspectionQuantity());

                    // 新任务的不合格数量为0（只分配合格数量）
                    newTask.setUnqualifiedQuantity(0);

                    // 新任务的完成数量等于合格数量
                    newTask.setCompletedQuantity(splitDto.getInspectionQuantity());

                    // 清空不合格编号（因为没有不合格数量）
                    newTask.setNonConformityNumber(null);

                    // 设置审计字段
                    newTask.setCodexTorchCreatorId(currentUserId);
                    newTask.setCodexTorchGroupId(currentUserGroupId);
                    newTask.setCodexTorchCreateDatetime(currentTime);
                    newTask.setCodexTorchUpdateDatetime(currentTime);
                    newTask.setCodexTorchDeleted(Constant.DEFAULT_NO);

                    // 插入新的生产任务
                    productionTaskMapper.insert(newTask);

                    // 复制子表数据
                    copyChildTableData(originalTask.getTaskNumber(), newTask.getTaskNumber(), currentUserId, currentUserGroupId, currentTime);
                }
            }
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    /**
     * 复制子表数据
     */
    private void copyChildTableData(String originalTaskNumber, String newTaskNumber, String currentUserId, String currentUserGroupId, Timestamp currentTime) {
        // 复制附件表数据
        List<ProductionTaskAttachments> attachmentsList = productionTaskAttachmentsMapper.selectList(
                new QueryWrapper<ProductionTaskAttachments>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProductionTaskAttachments attachment : attachmentsList) {
            ProductionTaskAttachments newAttachment = new ProductionTaskAttachments();
            BeanUtils.copyProperties(attachment, newAttachment);
            newAttachment.setId(null);
            newAttachment.setTaskNumber(newTaskNumber);
            newAttachment.setCodexTorchCreatorId(currentUserId);
            newAttachment.setCodexTorchGroupId(currentUserGroupId);
            newAttachment.setCodexTorchCreateDatetime(currentTime);
            newAttachment.setCodexTorchUpdateDatetime(currentTime);
            newAttachment.setCodexTorchDeleted(Constant.DEFAULT_NO);
            productionTaskAttachmentsMapper.insert(newAttachment);
        }

        // 复制试验数据表数据
        List<ProductionTaskTestData> testDataList = productionTaskTestDataMapper.selectList(
                new QueryWrapper<ProductionTaskTestData>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProductionTaskTestData testData : testDataList) {
            ProductionTaskTestData newTestData = new ProductionTaskTestData();
            BeanUtils.copyProperties(testData, newTestData);
            newTestData.setId(null);
            newTestData.setTaskNumber(newTaskNumber);
            newTestData.setCodexTorchCreatorId(currentUserId);
            newTestData.setCodexTorchGroupId(currentUserGroupId);
            newTestData.setCodexTorchCreateDatetime(currentTime);
            newTestData.setCodexTorchUpdateDatetime(currentTime);
            newTestData.setCodexTorchDeleted(Constant.DEFAULT_NO);
            productionTaskTestDataMapper.insert(newTestData);
        }

        // 复制设备信息表数据
        List<ProdTaskEqInfo> eqInfoList = prodTaskEqInfoMapper.selectList(
                new QueryWrapper<ProdTaskEqInfo>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProdTaskEqInfo eqInfo : eqInfoList) {
            ProdTaskEqInfo newEqInfo = new ProdTaskEqInfo();
            BeanUtils.copyProperties(eqInfo, newEqInfo);
            newEqInfo.setId(null);
            newEqInfo.setTaskNumber(newTaskNumber);
            newEqInfo.setCodexTorchCreatorId(currentUserId);
            newEqInfo.setCodexTorchGroupId(currentUserGroupId);
            newEqInfo.setCodexTorchCreateDatetime(currentTime);
            newEqInfo.setCodexTorchUpdateDatetime(currentTime);
            newEqInfo.setCodexTorchDeleted(Constant.DEFAULT_NO);
            prodTaskEqInfoMapper.insert(newEqInfo);
        }

        // 复制操作历史表数据
        List<ProdTaskOpHist> opHistList = prodTaskOpHistMapper.selectList(
                new QueryWrapper<ProdTaskOpHist>()
                        .eq("task_number", originalTaskNumber)
                        .eq("codex_torch_deleted", Constant.DEFAULT_NO)
        );
        for (ProdTaskOpHist opHist : opHistList) {
            ProdTaskOpHist newOpHist = new ProdTaskOpHist();
            BeanUtils.copyProperties(opHist, newOpHist);
            newOpHist.setId(null);
            newOpHist.setTaskNumber(newTaskNumber);
            newOpHist.setCodexTorchCreatorId(currentUserId);
            newOpHist.setCodexTorchGroupId(currentUserGroupId);
            newOpHist.setCodexTorchCreateDatetime(currentTime);
            newOpHist.setCodexTorchUpdateDatetime(currentTime);
            newOpHist.setCodexTorchDeleted(Constant.DEFAULT_NO);
            prodTaskOpHistMapper.insert(newOpHist);
        }
    }

    @Override
    public TorchResponse outsourcingPass(String processId) {
        //通过id查询,把状态变更为已外协，试验方式更新为外协
        ProductionTask productionTask = productionTaskMapper.selectById(processId);
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_YIWAIXIE);
        productionTask.setTestMethodology(DicConstant.ProductionOrder.TEST_METHODLOGY_OUTSORCEING);
        productionTaskMapper.updateById(productionTask);
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse outsourcingAccept(String processId) {
        //通过id查询,把状态变更为已外协，试验方式更新为外协
        ProductionTask productionTask = productionTaskMapper.selectById(processId);
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);
        productionTaskMapper.updateById(productionTask);
        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse scanCode(ScanCodeDTO scanCodeDto) {
        if (HuatekTools.isEmpty(scanCodeDto.getScannerGunNumber())) {
            throw new ServiceException("扫码枪编号不能为空");
        }
        if (HuatekTools.isEmpty(scanCodeDto.getWorkOrderNumber())) {
            throw new ServiceException("工单编号不能为空");
        }

        TorchResponse response = new TorchResponse();

        try {
            // 1. 根据扫码枪编号查询工作站信息
            TorchResponse<WorkstationVO> workstationResponse = workstationService.findWorkstationByScannerGunNumber(scanCodeDto.getScannerGunNumber());
            WorkstationVO workstation = workstationResponse.getData().getData();


            // 2. 用工作站和工单编号查询到productionTask信息
            ProductionTaskDTO taskQuery = new ProductionTaskDTO();
            taskQuery.setWorkstation(workstation.getId());
            taskQuery.setWorkOrderNumber(scanCodeDto.getWorkOrderNumber());

            TorchResponse<List<ProductionTaskVO>> taskListResponse = findProductionTaskPage(taskQuery);
            List<ProductionTaskVO> taskList = taskListResponse.getData().getData();

            if (taskList == null || taskList.isEmpty()) {
                throw new ServiceException("未找到对应的生产任务");
            }

            ProductionTaskVO productionTask = taskList.get(0);

            // 3. 检查ProductionTask状态，若任务已经取消或暂停，开始任务时需提醒，无法开始任务
            if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO.equals(productionTask.getStatus()) ||
                    DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING.equals(productionTask.getStatus())) {
                throw new ServiceException("任务已取消或暂停，无法开始任务");
            }

            // 4. 判断任务状态和前置条件
            if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI.equals(productionTask.getStatus())) {
                // 状态为未开始并且上一步工序和关联工单前置工序已经完成时，扫描条码，将状态修改为进行中
                boolean canStart = checkPreviousProcessCompleted(productionTask);

                if (canStart) {
                    // 将状态修改为进行中
                    ProductionTaskDTO updateDto = new ProductionTaskDTO();
                    updateDto.setId(productionTask.getId());
                    updateDto.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);
                    saveOrUpdate(updateDto);

                    // 插入操作记录
                    insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_START,
                            null, 0, null);

                    response.setMessage("任务已开始");
                } else {
                    throw new ServiceException("前置工序未完成，无法开始任务");
                }
            } else {
                // 其他状态时，扫描条码，不修改状态，弹出报工页面，任务状态默认进行中
                //response.setMessage("显示报工页面");
            }

        } catch (Exception e) {
            log.error("扫码处理异常", e);
            response.setMessage("系统异常，请联系管理员");
        }
        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public List<ProductionTaskViewVO> selectProductionTaskByProductionOrder(String workOrderNumber) {

        List<ProductionTaskViewVO> list = productionTaskMapper.selectProductionTaskByProductionOrder(workOrderNumber);
        if(ObjectUtils.isEmpty(list)){
            list = new ArrayList<>();
            return list;
        }
        List<String> taskNums = list.stream().map(ProductionTaskViewVO::getTaskNumber).collect(Collectors.toList());
        //查询任务设备
        QueryWrapper wrapper = new QueryWrapper();
        wrapper.in("task_number",taskNums);
        List<ProdTaskEqInfo> eqlist = prodTaskEqInfoMapper.selectList(wrapper);
        //查询试验数据
        List<ProductionTaskTestData> testdatas = productionTaskTestDataMapper.selectList(wrapper);
        List<ProductionTaskTestDataVO> testdatavos = new ArrayList<>();
        testdatas.stream().forEach(x->{
            ProductionTaskTestDataVO vo = new ProductionTaskTestDataVO();
            BeanUtils.copyProperties(x,vo);
            testdatavos.add(vo);
        });
        list.stream().forEach(x->{
            List<String> eqNum = eqlist.stream().filter(eq -> x.getTaskNumber().equals(eq.getTaskNumber())).map(ProdTaskEqInfo::getDeviceSerialNumber).collect(Collectors.toList());
            x.setDeviceNumbers(String.join(",",eqNum));
            x.setTaskTestData(testdatavos.stream().filter(t->t.getTaskNumber().equals(x.getTaskNumber())).collect(Collectors.toList()));
        });
        return list;
    }

    /**
     * 检查前置工序是否已完成
     *
     * @param productionTask 生产任务
     * @return 是否可以开始
     */
    private boolean checkPreviousProcessCompleted(ProductionTaskVO productionTask) {
        // 检查上一步工序：用workOrderNumber+executionSequence来判断
        if (productionTask.getExecutionSequence() != null && productionTask.getExecutionSequence() > 1) {
            // 直接查询前一个工序的状态
            String prevProcessStatus = productionTaskMapper.checkPreviousProcessStatus(
                productionTask.getWorkOrderNumber(),
                productionTask.getExecutionSequence()
            );

            if (prevProcessStatus != null) {
                // 如果找到前一个工序，检查其状态是否为已完成
                if (!DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG.equals(prevProcessStatus)) {
                    return false;
                }
            } else {
                // 如果没有找到上一步工序，说明前置工序不存在，可以开始
                log.warn("未找到上一步工序，工单号：{}，执行顺序：{}", productionTask.getWorkOrderNumber(), productionTask.getExecutionSequence() - 1);
            }
        }

        // 检查关联工单前置工序：用relatedWorkOrder和assoWoPredProc来判断
        if (!HuatekTools.isEmpty(productionTask.getRelatedWorkOrder()) &&
                !HuatekTools.isEmpty(productionTask.getAssoWoPredProc())) {

            // 直接查询关联工单前置工序的状态
            String relatedProcessStatus = productionTaskMapper.checkRelatedProcessStatus(
                productionTask.getRelatedWorkOrder(),
                productionTask.getAssoWoPredProc()
            );

            if (relatedProcessStatus != null) {
                // 如果找到关联工单前置工序，检查其状态是否为已完成
                if (!DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG.equals(relatedProcessStatus)) {
                    return false;
                }
            } else {
                // 如果没有找到关联工单前置工序，说明前置工序不存在，可以开始
                log.warn("未找到关联工单前置工序，关联工单号：{}，前置工序：{}", productionTask.getRelatedWorkOrder(), productionTask.getAssoWoPredProc());
            }
        }

        return true;
    }

    /**
     * 插入操作记录
     *
     * @param taskNumber 任务编号
     * @param operationType 操作类型
     * @param reason 原因
     * @param completedQuantity 已完成数量
     * @param comment 备注
     */
    private void insertOperationHistory(String taskNumber, String operationType, String reason, Integer completedQuantity, String comment) {
        try {
            ProdTaskOpHistDTO histDto = new ProdTaskOpHistDTO();
            histDto.setTaskNumber(taskNumber);
            histDto.setOperationType(operationType);
            histDto.setReason(reason);
            histDto.setCompletedQuantity(completedQuantity);
            histDto.setComment(comment);
            prodTaskOpHistService.saveOrUpdate(histDto);
        } catch (Exception e) {
            log.error("插入操作记录失败", e);
        }
    }

    @Override
    public TorchResponse startTask(StartTaskDTO startTaskDto) {
        if (HuatekTools.isEmpty(startTaskDto.getId())) {
            throw new ServiceException("生产任务ID不能为空");
        }

        TorchResponse response = new TorchResponse();

        try {
            // 1. 根据ID查询生产任务信息
            ProductionTask productionTask = productionTaskMapper.selectById(startTaskDto.getId());
            if (productionTask == null) {
                throw new ServiceException("未找到对应的生产任务");
            }

            // 转换为VO对象
            ProductionTaskVO productionTaskVO = new ProductionTaskVO();
            BeanUtils.copyProperties(productionTask, productionTaskVO);

            // 2. 检查ProductionTask状态，若任务已经取消或暂停，开始任务时需提醒，无法开始任务
            if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO.equals(productionTask.getStatus()) ||
                DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING.equals(productionTask.getStatus())) {
                throw new ServiceException("任务已取消或暂停，无法开始任务");
            }

            // 3. 判断任务状态和前置条件
            if (DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WEIKAISHI.equals(productionTask.getStatus())) {
                // 状态为未开始并且上一步工序和关联工单前置工序已经完成时，将状态修改为进行中
                boolean canStart = checkPreviousProcessCompleted(productionTaskVO);

                if (canStart) {
                    // 将状态修改为进行中
                    productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);
                    productionTaskMapper.updateById(productionTask);

                    // 插入操作记录
                    insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_START,
                            null, 0, null);

                    response.setMessage("任务已开始");
                } else {
                    throw new ServiceException("前置工序未完成，无法开始任务");
                }
            } else {
                // 其他状态时，点击开始，不修改状态，弹出报工页面，任务状态默认进行中

            }

        }catch (Exception e) {
            log.error("开始任务处理异常", e);
            response.setMessage("系统异常，请联系管理员");
        }

        response.setStatus(Constant.REQUEST_SUCCESS);
        return response;
    }

    @Override
    public TorchResponse<Long> getLastProcessQualifiedQuantity(String workOrderNumber) {
        if (HuatekTools.isEmpty(workOrderNumber)) {
            throw new ServiceException("工单编号不能为空");
        }

        // 直接从数据库查询该工单的最后一个工序的合格数量
        Long qualifiedQuantity = productionTaskMapper.getLastProcessQualifiedQuantity(workOrderNumber);

        TorchResponse<Long> result = new TorchResponse<>();
        result.getData().setData(qualifiedQuantity != null ? qualifiedQuantity : 0L);
        result.setStatus(Constant.REQUEST_SUCCESS);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse submitTask(SubmitTaskDTO submitTaskDto) {
        if (HuatekTools.isEmpty(submitTaskDto.getId())) {
            throw new ServiceException("任务ID不能为空");
        }

        // 查询任务信息
        ProductionTask task = productionTaskMapper.selectById(submitTaskDto.getId());
        if (task == null) {
            throw new ServiceException("任务不存在");
        }

        // 更新任务状态为待审批
        task.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_DAIAPPROVE);
        task.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
        task.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
        productionTaskMapper.updateById(task);

        // 插入操作记录
        insertOperationHistory(task.getTaskNumber(),
                              DicConstant.ProductionOrder.OPERATION_TYPE_SUBMIT,
                              null,
                              task.getCompletedQuantity(),
                              null);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("任务提交成功");
        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse approveTask(ApproveTaskDTO approveTaskDto) {
        if (HuatekTools.isEmpty(approveTaskDto.getIds()) || approveTaskDto.getIds().isEmpty()) {
            throw new ServiceException("任务ID列表不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (String taskId : approveTaskDto.getIds()) {
            try {
                // 查询任务信息
                ProductionTask task = productionTaskMapper.selectById(taskId);
                if (task == null) {
                    failCount++;
                    errorMessages.append("任务ID[").append(taskId).append("]不存在; ");
                    continue;
                }

                // 更新任务状态为完成
                task.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_WANCHENG);
                task.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
                task.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                productionTaskMapper.updateById(task);

                // 插入操作记录
                insertOperationHistory(task.getTaskNumber(),
                                      DicConstant.ProductionOrder.OPERATION_TYPE_WANCHENG,
                                      null,
                                      task.getCompletedQuantity(),
                        null);

                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMessages.append("任务ID[").append(taskId).append("]处理失败: ").append(e.getMessage()).append("; ");
                log.error("审批通过任务失败，任务ID: {}", taskId, e);
            }
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据任务数量调整返回消息
        if (approveTaskDto.getIds().size() == 1) {
            // 单个任务
            if (failCount == 0) {
                response.setMessage("任务审批通过成功");
            } else {
                response.setMessage("任务审批通过失败: " + errorMessages.toString());
            }
        } else {
            // 多个任务
            if (failCount == 0) {
                response.setMessage("批量审批通过成功，共处理" + successCount + "个任务");
            } else {
                response.setMessage("批量审批通过完成，成功" + successCount + "个，失败" + failCount + "个。失败原因: " + errorMessages.toString());
            }
        }

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public TorchResponse rejectTask(RejectTaskDTO rejectTaskDto) {
        if (HuatekTools.isEmpty(rejectTaskDto.getIds()) || rejectTaskDto.getIds().isEmpty()) {
            throw new ServiceException("任务ID列表不能为空");
        }
        if (HuatekTools.isEmpty(rejectTaskDto.getReason())) {
            throw new ServiceException("驳回原因不能为空");
        }

        int successCount = 0;
        int failCount = 0;
        StringBuilder errorMessages = new StringBuilder();

        for (String taskId : rejectTaskDto.getIds()) {
            try {
                // 查询任务信息
                ProductionTask task = productionTaskMapper.selectById(taskId);
                if (task == null) {
                    failCount++;
                    errorMessages.append("任务ID[").append(taskId).append("]不存在; ");
                    continue;
                }

                // 更新任务状态为驳回
                task.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_BOHUI);
                task.setCodexTorchUpdater(SecurityContextHolder.getCurrentUserId());
                task.setCodexTorchUpdateDatetime(new Timestamp(System.currentTimeMillis()));
                productionTaskMapper.updateById(task);

                // 插入操作记录
                insertOperationHistory(task.getTaskNumber(),
                                      DicConstant.ProductionOrder.OPERATION_TYPE_BOHUI,
                                      rejectTaskDto.getReason(),
                                      task.getCompletedQuantity(),
                                      null);

                successCount++;
            } catch (Exception e) {
                failCount++;
                errorMessages.append("任务ID[").append(taskId).append("]处理失败: ").append(e.getMessage()).append("; ");
                log.error("审批驳回任务失败，任务ID: {}", taskId, e);
            }
        }

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);

        // 根据任务数量调整返回消息
        if (rejectTaskDto.getIds().size() == 1) {
            // 单个任务
            if (failCount == 0) {
                response.setMessage("任务审批驳回成功");
            } else {
                response.setMessage("任务审批驳回失败: " + errorMessages.toString());
            }
        } else {
            // 多个任务
            if (failCount == 0) {
                response.setMessage("批量审批驳回成功，共处理" + successCount + "个任务");
            } else {
                response.setMessage("批量审批驳回完成，成功" + successCount + "个，失败" + failCount + "个。失败原因: " + errorMessages.toString());
            }
        }

        return response;
    }

    @Override
    public TorchResponse<List<UnqualifiedProcessVO>> getUnqualifiedTaskInfo(String workOrderNumber) {
        if (HuatekTools.isEmpty(workOrderNumber)) {
            throw new ServiceException("工单编号不能为空");
        }

        // 直接从数据库查询该工单的所有不合格工序信息
        List<UnqualifiedProcessVO> unqualifiedProcesses = productionTaskMapper.getWorkOrderQualityInfo(workOrderNumber);

        TorchResponse<List<UnqualifiedProcessVO>> result = new TorchResponse<>();
        result.getData().setData(unqualifiedProcesses != null ? unqualifiedProcesses : new ArrayList<>());
        result.setStatus(Constant.REQUEST_SUCCESS);
        return result;
    }

    @Override
    public TorchResponse pauseTask(TaskOperationDTO taskOperationDto) {
        if (HuatekTools.isEmpty(taskOperationDto.getId())) {
            throw new ServiceException("生产任务ID不能为空");
        }

        ProductionTask productionTask = productionTaskMapper.selectById(taskOperationDto.getId());
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务");
        }

        // 修改状态为暂停
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_ZANTING);
        productionTask.setPauseReason(taskOperationDto.getReason());

        productionTaskMapper.updateById(productionTask);

        // 插入操作记录
        insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_PAUSE,
            taskOperationDto.getReason(), productionTask.getCompletedQuantity(), taskOperationDto.getComment());

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("任务已暂停");
        return response;
    }

    @Override
    public TorchResponse resumeTask(String id) {
        if (HuatekTools.isEmpty(id)) {
            throw new ServiceException("生产任务ID不能为空");
        }

        ProductionTask productionTask = productionTaskMapper.selectById(id);
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务");
        }

        // 修改状态为进行中
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_JINXINGZHONG);
        productionTaskMapper.updateById(productionTask);

        // 插入操作记录
        insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_RESUME,
            null, productionTask.getCompletedQuantity(), null);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("任务已恢复");
        return response;
    }

    @Override
    public TorchResponse cancelTask(TaskOperationDTO taskOperationDto) {
        if (HuatekTools.isEmpty(taskOperationDto.getId())) {
            throw new ServiceException("生产任务ID不能为空");
        }

        ProductionTask productionTask = productionTaskMapper.selectById(taskOperationDto.getId());
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务");
        }

        // 修改状态为取消
        productionTask.setStatus(DicConstant.ProductionOrder.PRODUCTION_TASK_STATUS_QUXIAO);
        productionTaskMapper.updateById(productionTask);

        // 插入操作记录
        insertOperationHistory(productionTask.getTaskNumber(), DicConstant.ProductionOrder.OPERATION_TYPE_CANCEL,
            taskOperationDto.getReason(), null, taskOperationDto.getComment());

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("任务已取消");
        return response;
    }

    @Override
    public TorchResponse clearPdaWarning(String id) {
        if (HuatekTools.isEmpty(id)) {
            throw new ServiceException("生产任务ID不能为空");
        }

        ProductionTask productionTask = productionTaskMapper.selectById(id);
        if (productionTask == null) {
            throw new ServiceException("未找到对应的生产任务");
        }

        productionTask.setPdaWarning(BusinessConstant.PRODUCTIONTASK_YJ_NO);
        productionTaskMapper.updateById(productionTask);

        TorchResponse response = new TorchResponse();
        response.setStatus(Constant.REQUEST_SUCCESS);
        response.setMessage("PDA预警已消除");
        return response;
    }
}
